# MQTT功能使用说明

## 概述

cloud-dam-biz工程已集成完整的MQTT功能，支持消息发布和订阅。本文档介绍如何配置和使用MQTT功能。

## 功能特性

- ✅ MQTT消息发布
- ✅ MQTT消息订阅
- ✅ 自动重连机制
- ✅ 多主题批量订阅
- ✅ 消息处理器机制
- ✅ QoS等级支持
- ✅ 保留消息支持
- ✅ 通配符订阅支持
- ✅ 连接状态监控
- ✅ REST API接口

## 配置说明

### 1. 依赖配置

项目已自动包含以下依赖：
```xml
<dependency>
    <groupId>org.springframework.integration</groupId>
    <artifactId>spring-integration-mqtt</artifactId>
</dependency>
<dependency>
    <groupId>org.eclipse.paho</groupId>
    <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
    <version>1.2.5</version>
</dependency>
```

### 2. 配置文件

在 `application.yaml` 中配置MQTT连接参数：

```yaml
mqtt:
  broker:
    url: tcp://localhost:1883  # MQTT Broker地址
  client:
    id: dam-client  # 客户端ID前缀
  username:  # MQTT用户名（可选）
  password:  # MQTT密码（可选）
  connection:
    timeout: 10  # 连接超时时间（秒）
  keep:
    alive:
      interval: 60  # 心跳间隔（秒）
  clean:
    session: true  # 是否清除会话
  automatic:
    reconnect: true  # 是否自动重连
  default:
    topic: dam/default  # 默认发布主题
    qos: 1  # 默认QoS等级
    retained: false  # 默认是否保留消息
    subscribe:
      qos: 1  # 默认订阅QoS等级
  auto:
    subscribe:
      topics: dam/data,dam/alarm  # 自动订阅的主题
```

## 使用方式

### 1. 服务注入

```java
@Resource
private MqttService mqttService;
```

### 2. 发布消息

```java
// 发布消息到指定主题
mqttService.publishMessage("dam/data/device001", "Hello MQTT!");

// 发布消息到指定主题（指定QoS和retained）
mqttService.publishMessage("dam/alarm/device001", "Alarm message", 2, true);

// 发布消息到默认主题
mqttService.publishToDefaultTopic("Default message");
```

### 3. 订阅主题

```java
// 订阅主题（使用默认QoS）
mqttService.subscribeTopic("dam/data/+");

// 订阅主题（指定QoS）
mqttService.subscribeTopic("dam/alarm/#", 2);

// 批量订阅主题
List<String> topics = Arrays.asList("dam/data/+", "dam/alarm/#", "dam/status/+");
mqttService.subscribeTopics(topics);

// 取消订阅
mqttService.unsubscribeTopic("dam/data/+");
```

### 4. 消息处理器

```java
// 添加消息处理器
mqttService.addMessageHandler("dam/data/+", new MqttSubscriber.MqttMessageHandler() {
    @Override
    public void handleMessage(String topic, String payload, MqttMessage message) {
        log.info("收到数据消息: topic={}, payload={}", topic, payload);
        // 处理消息逻辑
    }
});
```

### 5. 连接状态检查

```java
// 检查连接状态
boolean connected = mqttService.isConnected();
```

## REST API接口

### 发布消息
```
POST /admin-api/dam/mqtt/publish
参数: topic, payload, qos, retained
```

### 订阅主题
```
POST /admin-api/dam/mqtt/subscribe
参数: topic, qos
```

### 取消订阅
```
POST /admin-api/dam/mqtt/unsubscribe
参数: topic
```

### 批量订阅
```
POST /admin-api/dam/mqtt/subscribe/batch
Body: ["topic1", "topic2", "topic3"]
```

### 获取连接状态
```
GET /admin-api/dam/mqtt/status
```

### 功能测试
```
POST /admin-api/dam/mqtt/test
```

## 主题命名规范

建议使用以下主题命名规范：

- 设备数据：`dam/data/device/{deviceId}`
- 设备告警：`dam/alarm/device/{deviceId}`
- 设备控制：`dam/control/device/{deviceId}`
- 设备状态：`dam/status/device/{deviceId}`
- 项目数据：`dam/data/project/{projectId}`

## 工具类使用

```java
// 验证主题名称
boolean valid = MqttUtils.isValidTopic("dam/data/device001");

// 构建设备数据主题
String topic = MqttUtils.buildDeviceDataTopic("device001");

// 主题匹配检查
boolean match = MqttUtils.isTopicMatch("dam/data/device001", "dam/data/+");

// 从主题中提取设备ID
String deviceId = MqttUtils.extractDeviceIdFromTopic("dam/data/device/device001");
```

## 注意事项

1. **连接配置**：确保MQTT Broker地址和端口正确
2. **认证信息**：如果MQTT Broker需要认证，请配置用户名和密码
3. **主题权限**：确保客户端有相应主题的发布和订阅权限
4. **QoS选择**：根据业务需求选择合适的QoS等级
5. **消息大小**：注意MQTT消息大小限制
6. **连接数限制**：注意MQTT Broker的连接数限制

## 故障排查

1. **连接失败**：检查网络连接和Broker配置
2. **认证失败**：检查用户名和密码配置
3. **订阅失败**：检查主题权限和主题格式
4. **消息丢失**：检查QoS设置和网络稳定性
5. **性能问题**：检查消息频率和处理器性能

## 扩展开发

如需扩展MQTT功能，可以：

1. 实现自定义消息处理器
2. 添加消息序列化/反序列化
3. 集成消息持久化
4. 添加消息路由规则
5. 实现消息过滤器

## 示例代码

完整的使用示例请参考：
- `MqttController.java` - REST API示例
- `DefaultMqttMessageHandler.java` - 消息处理器示例
- `MqttUtils.java` - 工具类示例
