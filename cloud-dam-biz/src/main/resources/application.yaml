spring:
  main:
    allow-circular-references: true # 允许循环依赖，因为项目是三层架构，无法避免这个情况。
    allow-bean-definition-overriding: true # 允许 Bean 覆盖，例如说 Feign 等会存在重复定义的服务

  # Servlet 配置
  servlet:
    # 文件上传相关配置项
    multipart:
      max-file-size: 16MB # 单个文件大小
      max-request-size: 32MB # 设置总上传的文件大小
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER # 解决 SpringFox 与 SpringBoot 2.6.x 不兼容的问题，参见 SpringFoxHandlerProviderBeanPostProcessor 类

  # Jackson 配置项
  jackson:
    serialization:
      write-dates-as-timestamps: true # 设置 LocalDateTime 的格式，使用时间戳
      write-date-timestamps-as-nanoseconds: false # 设置不使用 nanoseconds 的格式。例如说 **********.401，而是直接 **********401
      write-durations-as-timestamps: true # 设置 Duration 的格式，使用时间戳
      fail-on-empty-beans: false # 允许序列化无属性的 Bean

  # Cache 配置项
  cache:
    type: REDIS
    redis:
      time-to-live: 1h # 设置过期时间为 1 小时

--- #################### 接口文档配置 ####################

springdoc:
  api-docs:
    enabled: true # 1. 是否开启 Swagger 接文档的元数据
    path: /v3/api-docs
  swagger-ui:
    enabled: true # 2.1 是否开启 Swagger 文档的官方 UI 界面
    path: /swagger-ui.html
  default-flat-param-object: true # 参见 https://doc.xiaominfo.com/docs/faq/v4/knife4j-parameterobject-flat-param 文档

knife4j:
  enable: true # 2.2 是否开启 Swagger 文档的 Knife4j UI 界面
  setting:
    language: zh_cn

# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
  global-config:
    db-config:
      id-type: AUTO # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
      #      id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
      #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
      #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    banner: false # 关闭控制台的 Banner 打印
  type-aliases-package: ${bjycloud.info.base-package}.dal.dataobject
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

mybatis-plus-join:
  banner: false # 关闭控制台的 Banner 打印

# Spring Data Redis 配置
spring:
  data:
    redis:
      repositories:
        enabled: false # 项目未使用到 Spring Data Redis 的 Repository，所以直接禁用，保证启动速度

# VO 转换（数据翻译）相关
easy-trans:
  is-enable-global: true # 启用全局翻译（拦截所有 SpringMVC ResponseBody 进行自动翻译 )。如果对于性能要求很高可关闭此配置，或通过 @IgnoreTrans 忽略某个接口
  is-enable-cloud: false # 禁用 TransType.RPC 微服务模式

--- #################### RPC 远程调用相关配置 ####################

--- #################### 定时任务相关配置 ####################
xxl:
  job:
    executor:
      appname: ${spring.application.name} # 执行器 AppName
      logpath: ${user.home}/logs/xxl-job/${spring.application.name} # 执行器运行日志文件存储磁盘路径
    accessToken: default_token # 执行器通讯TOKEN

--- #################### MQTT相关配置 ####################

mqtt:
  broker:
    url: tcp://localhost:1883  # MQTT Broker地址，支持多个地址用逗号分隔
  client:
    id: dam-client  # 客户端ID前缀，实际使用时会添加UUID后缀
  username:  # MQTT用户名，如果不需要认证可以留空
  password:  # MQTT密码，如果不需要认证可以留空
  connection:
    timeout: 10  # 连接超时时间（秒）
  keep:
    alive:
      interval: 60  # 心跳间隔（秒）
  clean:
    session: true  # 是否清除会话
  automatic:
    reconnect: true  # 是否自动重连
  default:
    topic: dam/default  # 默认发布主题
    qos: 1  # 默认QoS等级
    retained: false  # 默认是否保留消息
    subscribe:
      qos: 1  # 默认订阅QoS等级
  auto:
    subscribe:
      topics:  # 自动订阅的主题，多个主题用逗号分隔，例如: dam/data,dam/alarm

--- #################### 相关配置 ####################

bjycloud:
  info:
    version: 1.0.0
    base-package: cn.powerchina.bjy.link.dam
  web:
    admin-ui:
      url: http://dashboard.bjylink.cn # Admin 管理后台 UI 的地址
  swagger:
    title: 管理后台
    description: 提供管理员管理的所有功能
    version: ${bjylink.info.version}
    base-package: ${bjycloud.info.base-package}
  tenant: # 多租户相关配置项
    enable: true
    ignore-tables:
      - dam_auth_device
      - dam_auth_product
      - dam_device
      - dam_device_collect_log
      - dam_device_strategy
      - dam_formula_model
      - dam_formula_point
      - dam_instrument
      - dam_instrument_account
      - dam_instrument_model
      - dam_instrument_param
      - dam_point
      - dam_point_alarm
      - dam_point_alarm_config
      - dam_point_data
      - dam_point_data_json
      - dam_point_data_import
      - dam_point_evaluate
      - dam_point_formula
      - dam_point_param
      - dam_project
      - dam_project_category
      - dam_project_info
      - dam_project_menu
      - dam_project_role
      - dam_project_user
      - dam_user
      - dam_index_head
      - dam_index_middle_bottom
      - dam_point_evaluate_extreme
      - dam_instrument_param_template
      - dam_instrument_model_template
      - dam_instrument_template
      - dam_import_node
      - dam_import_task
      - dam_import_file
      - dam_import_error_log
      - dam_import_config
    ignore-urls:
      - /rpc-api/dam/xxljob/deviceCollectLog/run
      - /rpc-api/dam/xxljob/indexStatistics/run
      - /admin-api/dam/device/secret
debug: false
