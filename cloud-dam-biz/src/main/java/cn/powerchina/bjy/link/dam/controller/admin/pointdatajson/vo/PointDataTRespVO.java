package cn.powerchina.bjy.link.dam.controller.admin.pointdatajson.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 测点数据json Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointDataTRespVO {

    @Schema(description = "项目id", example = "19338")
    private Long projectId;

    @Schema(description = "仪器类型id", example = "3871")
    private Long instrumentId;

    @Schema(description = "测点id", example = "24279")
    private Long pointId;

    @Schema(description = "监测时间")
    private String pointTime;

    @Schema(description = "监测开始时间")
    private String startPointTime;

    @Schema(description = "监测结束时间")
    private String endPointTime;

    @Schema(description = "采集类型(1：自动化采集，2：人工录入）", example = "1")
    private Integer dataType;

    @Schema(description = "数据状态(0：未判定，1：正常数据，2：异常，3：错误数据）", example = "1")
    private Integer dataStatus;

    @Schema(description = "审核状态")
    private Integer reviewStatus;

    private Integer pageNo;

    private Integer pageSize;

    private Integer offset;  // 分页偏移量
    private Integer limit;   // 每页记录数

}