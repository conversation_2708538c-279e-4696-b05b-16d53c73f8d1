package cn.powerchina.bjy.link.dam.service.mqtt;

import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Simple MQTT Service Implementation
 * 
 * <AUTHOR>
 */
@Service
public class SimpleMqttService {

    @Value("${mqtt.broker.url:tcp://localhost:1883}")
    private String brokerUrl;

    @Value("${mqtt.client.id:dam-client}")
    private String clientId;

    @Value("${mqtt.username:}")
    private String username;

    @Value("${mqtt.password:}")
    private String password;

    @Value("${mqtt.default.topic:dam/default}")
    private String defaultTopic;

    private MqttClient mqttClient;
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<MessageHandler>> messageHandlers = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            connect();
            System.out.println("SimpleMqttService initialized successfully");
        } catch (Exception e) {
            System.err.println("Failed to initialize SimpleMqttService: " + e.getMessage());
        }
    }

    private void connect() throws MqttException {
        String uniqueClientId = clientId + "-" + UUID.randomUUID().toString().replace("-", "");
        String firstBrokerUrl = brokerUrl.contains(",") ? brokerUrl.split(",")[0] : brokerUrl;
        
        mqttClient = new MqttClient(firstBrokerUrl, uniqueClientId);
        
        MqttConnectOptions options = new MqttConnectOptions();
        if (username != null && !username.trim().isEmpty()) {
            options.setUserName(username);
        }
        if (password != null && !password.trim().isEmpty()) {
            options.setPassword(password.toCharArray());
        }
        options.setCleanSession(true);
        options.setAutomaticReconnect(true);
        options.setConnectionTimeout(10);
        options.setKeepAliveInterval(60);

        mqttClient.setCallback(new MqttCallback() {
            @Override
            public void connectionLost(Throwable cause) {
                System.err.println("MQTT connection lost: " + cause.getMessage());
            }

            @Override
            public void messageArrived(String topic, MqttMessage message) throws Exception {
                handleMessage(topic, message);
            }

            @Override
            public void deliveryComplete(IMqttDeliveryToken token) {
                System.out.println("Message delivery complete");
            }
        });

        mqttClient.connect(options);
        System.out.println("MQTT client connected: " + firstBrokerUrl);
    }

    /**
     * Publish message to topic
     */
    public void publish(String topic, String payload) {
        publish(topic, payload, 1, false);
    }

    /**
     * Publish message to topic with QoS and retained flag
     */
    public void publish(String topic, String payload, int qos, boolean retained) {
        if (topic == null || topic.trim().isEmpty()) {
            System.err.println("Topic cannot be empty");
            return;
        }

        if (payload == null) {
            payload = "";
        }

        try {
            if (!mqttClient.isConnected()) {
                mqttClient.reconnect();
            }

            MqttMessage message = new MqttMessage(payload.getBytes(StandardCharsets.UTF_8));
            message.setQos(qos);
            message.setRetained(retained);

            mqttClient.publish(topic, message);
            System.out.println("Message published successfully: topic=" + topic + ", payload=" + payload);
        } catch (MqttException e) {
            System.err.println("Failed to publish message: topic=" + topic + ", error=" + e.getMessage());
            throw new RuntimeException("Failed to publish MQTT message", e);
        }
    }

    /**
     * Publish message to default topic
     */
    public void publishToDefaultTopic(String payload) {
        publish(defaultTopic, payload);
    }

    /**
     * Subscribe to topic
     */
    public void subscribe(String topic) {
        subscribe(topic, 1);
    }

    /**
     * Subscribe to topic with QoS
     */
    public void subscribe(String topic, int qos) {
        if (topic == null || topic.trim().isEmpty()) {
            System.err.println("Topic cannot be empty");
            return;
        }

        try {
            if (!mqttClient.isConnected()) {
                mqttClient.reconnect();
            }

            mqttClient.subscribe(topic, qos);
            System.out.println("Subscribed to topic successfully: topic=" + topic + ", qos=" + qos);
        } catch (MqttException e) {
            System.err.println("Failed to subscribe to topic: topic=" + topic + ", error=" + e.getMessage());
            throw new RuntimeException("Failed to subscribe to MQTT topic", e);
        }
    }

    /**
     * Unsubscribe from topic
     */
    public void unsubscribe(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            System.err.println("Topic cannot be empty");
            return;
        }

        try {
            if (mqttClient.isConnected()) {
                mqttClient.unsubscribe(topic);
                messageHandlers.remove(topic);
                System.out.println("Unsubscribed from topic successfully: topic=" + topic);
            }
        } catch (MqttException e) {
            System.err.println("Failed to unsubscribe from topic: topic=" + topic + ", error=" + e.getMessage());
            throw new RuntimeException("Failed to unsubscribe from MQTT topic", e);
        }
    }

    /**
     * Add message handler for topic
     */
    public void addMessageHandler(String topic, MessageHandler handler) {
        messageHandlers.computeIfAbsent(topic, k -> new CopyOnWriteArrayList<>()).add(handler);
        System.out.println("Message handler added for topic: " + topic);
    }

    /**
     * Remove message handler for topic
     */
    public void removeMessageHandler(String topic, MessageHandler handler) {
        CopyOnWriteArrayList<MessageHandler> handlers = messageHandlers.get(topic);
        if (handlers != null) {
            handlers.remove(handler);
            if (handlers.isEmpty()) {
                messageHandlers.remove(topic);
            }
        }
        System.out.println("Message handler removed for topic: " + topic);
    }

    /**
     * Check if client is connected
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * Handle received message
     */
    private void handleMessage(String topic, MqttMessage message) {
        try {
            String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
            System.out.println("Message received: topic=" + topic + ", payload=" + payload);

            // Find matching message handlers
            for (String handlerTopic : messageHandlers.keySet()) {
                if (isTopicMatch(topic, handlerTopic)) {
                    CopyOnWriteArrayList<MessageHandler> handlers = messageHandlers.get(handlerTopic);
                    for (MessageHandler handler : handlers) {
                        try {
                            handler.handleMessage(topic, payload, message);
                        } catch (Exception e) {
                            System.err.println("Message handler failed: topic=" + topic + ", error=" + e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Failed to handle message: topic=" + topic + ", error=" + e.getMessage());
        }
    }

    /**
     * Check if topic matches pattern (simple implementation)
     */
    private boolean isTopicMatch(String actualTopic, String filterTopic) {
        if (filterTopic.equals("#")) {
            return true;
        }
        if (filterTopic.equals(actualTopic)) {
            return true;
        }
        // Simple wildcard matching - can be enhanced
        return false;
    }

    @PreDestroy
    public void destroy() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                System.out.println("MQTT client disconnected");
            }
        } catch (Exception e) {
            System.err.println("Failed to disconnect MQTT client: " + e.getMessage());
        }
    }

    /**
     * Message Handler Interface
     */
    public interface MessageHandler {
        void handleMessage(String topic, String payload, MqttMessage message);
    }
}
