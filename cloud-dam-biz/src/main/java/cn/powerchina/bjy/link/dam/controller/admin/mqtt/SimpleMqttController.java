package cn.powerchina.bjy.link.dam.controller.admin.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.service.mqtt.SimpleMqttService;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple MQTT Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin-api/dam/simple-mqtt")
public class SimpleMqttController {

    @Resource
    private SimpleMqttService simpleMqttService;

    // Store received messages for demo
    private final Map<String, String> receivedMessages = new ConcurrentHashMap<>();

    @PostMapping("/publish")
    public CommonResult<Boolean> publishMessage(
            @RequestParam String topic,
            @RequestParam String payload,
            @RequestParam(defaultValue = "1") int qos,
            @RequestParam(defaultValue = "false") boolean retained) {
        
        try {
            simpleMqttService.publish(topic, payload, qos, retained);
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error("Failed to publish message: " + e.getMessage());
        }
    }

    @PostMapping("/publish/default")
    public CommonResult<Boolean> publishToDefaultTopic(@RequestParam String payload) {
        try {
            simpleMqttService.publishToDefaultTopic(payload);
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error("Failed to publish message: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe")
    public CommonResult<Boolean> subscribeTopic(
            @RequestParam String topic,
            @RequestParam(defaultValue = "1") int qos) {
        
        try {
            simpleMqttService.subscribe(topic, qos);
            
            // Add message handler to store received messages
            simpleMqttService.addMessageHandler(topic, (receivedTopic, receivedPayload, message) -> {
                receivedMessages.put(receivedTopic + "_" + System.currentTimeMillis(), receivedPayload);
                System.out.println("Message received via API: topic=" + receivedTopic + ", payload=" + receivedPayload);
            });
            
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error("Failed to subscribe to topic: " + e.getMessage());
        }
    }

    @PostMapping("/unsubscribe")
    public CommonResult<Boolean> unsubscribeTopic(@RequestParam String topic) {
        try {
            simpleMqttService.unsubscribe(topic);
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error("Failed to unsubscribe from topic: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    public CommonResult<Boolean> getConnectionStatus() {
        try {
            boolean connected = simpleMqttService.isConnected();
            return CommonResult.success(connected);
        } catch (Exception e) {
            return CommonResult.error("Failed to get connection status: " + e.getMessage());
        }
    }

    @GetMapping("/messages")
    public CommonResult<Map<String, String>> getReceivedMessages() {
        try {
            return CommonResult.success(receivedMessages);
        } catch (Exception e) {
            return CommonResult.error("Failed to get messages: " + e.getMessage());
        }
    }

    @DeleteMapping("/messages")
    public CommonResult<Boolean> clearReceivedMessages() {
        try {
            receivedMessages.clear();
            return CommonResult.success(true);
        } catch (Exception e) {
            return CommonResult.error("Failed to clear messages: " + e.getMessage());
        }
    }

    @PostMapping("/test")
    public CommonResult<String> testMqtt() {
        try {
            String testTopic = "dam/test";
            String testMessage = "Hello MQTT! Time: " + System.currentTimeMillis();
            
            // Subscribe to test topic first
            simpleMqttService.subscribe(testTopic);
            
            // Wait a moment to ensure subscription is complete
            Thread.sleep(1000);
            
            // Publish test message
            simpleMqttService.publish(testTopic, testMessage);
            
            return CommonResult.success("MQTT test completed. Published message to topic: " + testTopic + 
                    ", message: " + testMessage);
        } catch (Exception e) {
            return CommonResult.error("MQTT test failed: " + e.getMessage());
        }
    }
}
