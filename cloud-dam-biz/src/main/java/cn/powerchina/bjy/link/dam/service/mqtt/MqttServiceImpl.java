package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.transportTarget.TransportTargetApi;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * MQTT服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttServiceImpl implements MqttService {

    private List<String> topicList = new ArrayList<>();

    @Resource
    private TransportTargetApi transportTargetApi;

    @Resource
    private MqttPublisher mqttPublisher;

    @Resource
    private MqttSubscriber mqttSubscriber;

    @PostConstruct
    @Override
    public void getTopic() {
        try {
            CommonResult<List<TransportTargetRespDTO>> commonResult = transportTargetApi.getTransportTarget(null);
            if (commonResult != null && commonResult.getData() != null) {
                commonResult.getData().forEach(temp -> {
                    if (temp != null && temp.getTopic() != null) {
                        topicList.add(temp.getTopic());
                    }
                });
                log.info("获取到主题列表: {}", topicList);
            }
        } catch (Exception e) {
            log.error("获取主题列表失败", e);
        }
    }

    @Override
    public void publishMessage(String topic, String payload) {
        mqttPublisher.publish(topic, payload);
    }

    @Override
    public void publishMessage(String topic, String payload, int qos, boolean retained) {
        mqttPublisher.publish(topic, payload, qos, retained);
    }

    @Override
    public void publishToDefaultTopic(String payload) {
        mqttPublisher.publishToDefaultTopic(payload);
    }

    @Override
    public void subscribeTopic(String topic) {
        mqttSubscriber.subscribe(topic);
    }

    @Override
    public void subscribeTopic(String topic, int qos) {
        mqttSubscriber.subscribe(topic, qos);
    }

    @Override
    public void unsubscribeTopic(String topic) {
        mqttSubscriber.unsubscribe(topic);
    }

    @Override
    public void subscribeTopics(List<String> topics) {
        if (topics != null && !topics.isEmpty()) {
            for (String topic : topics) {
                if (topic != null && !topic.trim().isEmpty()) {
                    subscribeTopic(topic.trim());
                }
            }
        }
    }

    @Override
    public boolean isConnected() {
        return mqttPublisher.isConnected() && mqttSubscriber.isConnected();
    }

    @Override
    public void addMessageHandler(String topic, MqttSubscriber.MqttMessageHandler handler) {
        mqttSubscriber.addMessageHandler(topic, handler);
    }

    @Override
    public void removeMessageHandler(String topic, MqttSubscriber.MqttMessageHandler handler) {
        mqttSubscriber.removeMessageHandler(topic, handler);
    }

    /**
     * 获取已配置的主题列表
     *
     * @return 主题列表
     */
    public List<String> getTopicList() {
        return new ArrayList<>(topicList);
    }

    /**
     * 发布JSON消息
     *
     * @param topic  主题
     * @param object 要序列化的对象
     */
    public void publishJsonMessage(String topic, Object object) {
        mqttPublisher.publishJson(topic, object);
    }

    /**
     * 批量发布消息
     *
     * @param messages 消息列表
     */
    public void publishBatchMessages(List<MqttPublisher.MqttMessageInfo> messages) {
        mqttPublisher.publishBatch(messages);
    }
}
