package cn.powerchina.bjy.link.dam.service.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.iot.api.transportTarget.TransportTargetApi;
import cn.powerchina.bjy.link.iot.api.transportTarget.dto.TransportTargetRespDTO;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: mqtt服务
 * @Author:
 * @CreateDate:
 */
@Service
public class MqttServiceImpl implements MqttService {

    private List<String> topicList = new ArrayList<>();

    @Resource
    private TransportTargetApi transportTargetApi;

    @PostConstruct
    @Override
    public void getTopic() {
        CommonResult<List<TransportTargetRespDTO>> commonResult = transportTargetApi.getTransportTarget(null);
        commonResult.getData().forEach(temp->{
            topicList.add(temp.getTopic());
        });
    }
}
