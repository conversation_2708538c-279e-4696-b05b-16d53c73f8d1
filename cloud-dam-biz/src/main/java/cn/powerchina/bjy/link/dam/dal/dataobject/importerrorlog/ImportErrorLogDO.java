package cn.powerchina.bjy.link.dam.dal.dataobject.importerrorlog;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.powerchina.bjy.cloud.framework.mybatis.core.dataobject.BaseDO;

/**
 * 错误日志 DO
 *
 * <AUTHOR>
 */
@TableName("dam_import_error_log")
@KeySequence("dam_import_error_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportErrorLogDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 测点id
     */
    private Long pointId;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 任务执行时间
     */
    private LocalDateTime executeTime;
    /**
     * 工作表名称
     */
    private String sheetName;
    /**
     * 错误行
     */
    private String errorLine;
    /**
     * 错误信息
     */
    private String errorRemark;

    private Long nodeId;

    @Schema(description = "项目id", example = "8231")
    private Long projectId;

    /**
     * 测点编号
     */
    private String pointCode;
}