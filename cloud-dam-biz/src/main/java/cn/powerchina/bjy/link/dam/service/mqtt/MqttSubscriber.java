package cn.powerchina.bjy.link.dam.service.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * MQTT消息订阅服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttSubscriber {

    @Resource
    private MqttClient mqttClient;

    @Value("${mqtt.default.subscribe.qos:1}")
    private int defaultSubscribeQos;

    @Value("${mqtt.auto.subscribe.topics:}")
    private String autoSubscribeTopics;

    // 存储订阅的主题和对应的消息处理器
    private final ConcurrentHashMap<String, CopyOnWriteArrayList<MqttMessageHandler>> topicHandlers = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        try {
            // 设置回调处理器
            mqttClient.setCallback(new MqttCallbackExtended() {
                @Override
                public void connectComplete(boolean reconnect, String serverURI) {
                    log.info("MQTT连接完成: reconnect={}, serverURI={}", reconnect, serverURI);
                    if (reconnect) {
                        // 重连后重新订阅所有主题
                        resubscribeAllTopics();
                    }
                }

                @Override
                public void connectionLost(Throwable cause) {
                    log.error("MQTT连接丢失", cause);
                }

                @Override
                public void messageArrived(String topic, MqttMessage message) throws Exception {
                    handleMessage(topic, message);
                }

                @Override
                public void deliveryComplete(IMqttDeliveryToken token) {
                    log.debug("MQTT消息投递完成: token={}", token);
                }
            });

            // 自动订阅配置的主题
            if (autoSubscribeTopics != null && !autoSubscribeTopics.trim().isEmpty()) {
                String[] topics = autoSubscribeTopics.split(",");
                for (String topic : topics) {
                    topic = topic.trim();
                    if (!topic.isEmpty()) {
                        subscribe(topic);
                    }
                }
            }

            log.info("MQTT订阅服务初始化完成");
        } catch (Exception e) {
            log.error("MQTT订阅服务初始化失败", e);
        }
    }

    /**
     * 订阅主题（使用默认QoS）
     *
     * @param topic 主题
     */
    public void subscribe(String topic) {
        subscribe(topic, defaultSubscribeQos);
    }

    /**
     * 订阅主题
     *
     * @param topic 主题
     * @param qos   服务质量等级
     */
    public void subscribe(String topic, int qos) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("订阅失败: topic不能为空");
            return;
        }

        try {
            // 检查客户端连接状态
            if (!mqttClient.isConnected()) {
                log.warn("MQTT客户端未连接，尝试重新连接...");
                mqttClient.reconnect();
            }

            mqttClient.subscribe(topic, qos);
            log.info("MQTT主题订阅成功: topic={}, qos={}", topic, qos);

        } catch (MqttException e) {
            log.error("MQTT主题订阅失败: topic={}, qos={}, error={}", topic, qos, e.getMessage(), e);
            throw new RuntimeException("MQTT主题订阅失败", e);
        }
    }

    /**
     * 批量订阅主题
     *
     * @param topics 主题数组
     * @param qos    服务质量等级数组
     */
    public void subscribe(String[] topics, int[] qos) {
        if (topics == null || topics.length == 0) {
            log.error("批量订阅失败: topics不能为空");
            return;
        }

        if (qos == null || qos.length != topics.length) {
            log.error("批量订阅失败: qos数组长度与topics不匹配");
            return;
        }

        try {
            // 检查客户端连接状态
            if (!mqttClient.isConnected()) {
                log.warn("MQTT客户端未连接，尝试重新连接...");
                mqttClient.reconnect();
            }

            mqttClient.subscribe(topics, qos);
            log.info("MQTT批量订阅成功: topics={}, qos={}", java.util.Arrays.toString(topics), java.util.Arrays.toString(qos));

        } catch (MqttException e) {
            log.error("MQTT批量订阅失败: topics={}, qos={}, error={}", 
                    java.util.Arrays.toString(topics), java.util.Arrays.toString(qos), e.getMessage(), e);
            throw new RuntimeException("MQTT批量订阅失败", e);
        }
    }

    /**
     * 取消订阅主题
     *
     * @param topic 主题
     */
    public void unsubscribe(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("取消订阅失败: topic不能为空");
            return;
        }

        try {
            if (mqttClient.isConnected()) {
                mqttClient.unsubscribe(topic);
                // 移除对应的消息处理器
                topicHandlers.remove(topic);
                log.info("MQTT主题取消订阅成功: topic={}", topic);
            }
        } catch (MqttException e) {
            log.error("MQTT主题取消订阅失败: topic={}, error={}", topic, e.getMessage(), e);
            throw new RuntimeException("MQTT主题取消订阅失败", e);
        }
    }

    /**
     * 添加消息处理器
     *
     * @param topic   主题（支持通配符）
     * @param handler 消息处理器
     */
    public void addMessageHandler(String topic, MqttMessageHandler handler) {
        topicHandlers.computeIfAbsent(topic, k -> new CopyOnWriteArrayList<>()).add(handler);
        log.info("添加MQTT消息处理器: topic={}, handler={}", topic, handler.getClass().getSimpleName());
    }

    /**
     * 移除消息处理器
     *
     * @param topic   主题
     * @param handler 消息处理器
     */
    public void removeMessageHandler(String topic, MqttMessageHandler handler) {
        CopyOnWriteArrayList<MqttMessageHandler> handlers = topicHandlers.get(topic);
        if (handlers != null) {
            handlers.remove(handler);
            if (handlers.isEmpty()) {
                topicHandlers.remove(topic);
            }
        }
        log.info("移除MQTT消息处理器: topic={}, handler={}", topic, handler.getClass().getSimpleName());
    }

    /**
     * 处理接收到的消息
     */
    private void handleMessage(String topic, MqttMessage message) {
        try {
            String payload = new String(message.getPayload(), StandardCharsets.UTF_8);
            log.info("收到MQTT消息: topic={}, payload={}, qos={}, retained={}", 
                    topic, payload, message.getQos(), message.isRetained());

            // 查找匹配的消息处理器
            for (String handlerTopic : topicHandlers.keySet()) {
                if (isTopicMatch(topic, handlerTopic)) {
                    CopyOnWriteArrayList<MqttMessageHandler> handlers = topicHandlers.get(handlerTopic);
                    for (MqttMessageHandler handler : handlers) {
                        try {
                            handler.handleMessage(topic, payload, message);
                        } catch (Exception e) {
                            log.error("消息处理器执行失败: topic={}, handler={}, error={}", 
                                    topic, handler.getClass().getSimpleName(), e.getMessage(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理MQTT消息失败: topic={}, error={}", topic, e.getMessage(), e);
        }
    }

    /**
     * 检查主题是否匹配（支持MQTT通配符）
     */
    private boolean isTopicMatch(String actualTopic, String filterTopic) {
        // 简单的通配符匹配实现
        if (filterTopic.equals("#")) {
            return true;
        }
        if (filterTopic.equals(actualTopic)) {
            return true;
        }
        // 这里可以实现更复杂的MQTT通配符匹配逻辑
        return false;
    }

    /**
     * 重新订阅所有主题
     */
    private void resubscribeAllTopics() {
        for (String topic : topicHandlers.keySet()) {
            try {
                subscribe(topic);
            } catch (Exception e) {
                log.error("重新订阅主题失败: topic={}", topic, e);
            }
        }
    }

    /**
     * 检查MQTT客户端连接状态
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    @PreDestroy
    public void destroy() {
        try {
            if (mqttClient != null && mqttClient.isConnected()) {
                mqttClient.disconnect();
                log.info("MQTT订阅服务已关闭");
            }
        } catch (Exception e) {
            log.error("关闭MQTT订阅服务失败", e);
        }
    }

    /**
     * MQTT消息处理器接口
     */
    public interface MqttMessageHandler {
        /**
         * 处理MQTT消息
         *
         * @param topic   主题
         * @param payload 消息内容
         * @param message 原始消息对象
         */
        void handleMessage(String topic, String payload, MqttMessage message);
    }
}
