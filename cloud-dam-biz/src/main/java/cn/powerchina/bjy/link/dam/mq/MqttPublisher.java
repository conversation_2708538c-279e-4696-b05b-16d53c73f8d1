package cn.powerchina.bjy.link.iot.mq;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class MqttPublisher {

    @Resource
    private MqttClient mqttClient;

    @Value("${mqtt.default.topic}")
    private String defaultTopic;

    public void publish(String topic, String payload) {
        publish(topic, payload, 1, false);
    }

    public void publish(String topic, String payload, int qos, boolean retained) {
        MqttMessage message = new MqttMessage(payload.getBytes());
        message.setQos(qos);
        message.setRetained(retained);
        try {
            mqttClient.publish(topic, message);
            log.info("发送消息成功，topic={}，message={} ", topic, message);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发送消息失败，topic={}，message={} ", topic, message);
        }
    }

    public void publishToDefaultTopic(String payload) {
        publish(defaultTopic, payload);
    }
}