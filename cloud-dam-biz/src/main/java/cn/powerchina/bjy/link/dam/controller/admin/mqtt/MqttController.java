package cn.powerchina.bjy.link.dam.controller.admin.mqtt;

import cn.powerchina.bjy.cloud.framework.common.pojo.CommonResult;
import cn.powerchina.bjy.link.dam.service.mqtt.MqttService;
import cn.powerchina.bjy.link.dam.service.mqtt.MqttSubscriber;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MQTT管理控制器
 * 
 * <AUTHOR>
 */
@Tag(name = "管理后台 - MQTT管理")
@RestController
@RequestMapping("/admin-api/dam/mqtt")
@Slf4j
public class MqttController {

    @Resource
    private MqttService mqttService;

    // 存储接收到的消息，用于演示
    private final Map<String, String> receivedMessages = new ConcurrentHashMap<>();

    @PostMapping("/publish")
    @Operation(summary = "发布MQTT消息")
    public CommonResult<Boolean> publishMessage(
            @Parameter(description = "主题", required = true) @RequestParam String topic,
            @Parameter(description = "消息内容", required = true) @RequestParam String payload,
            @Parameter(description = "QoS等级", required = false) @RequestParam(defaultValue = "1") int qos,
            @Parameter(description = "是否保留消息", required = false) @RequestParam(defaultValue = "false") boolean retained) {
        
        try {
            mqttService.publishMessage(topic, payload, qos, retained);
            log.info("通过API发布MQTT消息成功: topic={}, payload={}", topic, payload);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("通过API发布MQTT消息失败: topic={}, payload={}", topic, payload, e);
            return CommonResult.error("发布消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/publish/default")
    @Operation(summary = "发布消息到默认主题")
    public CommonResult<Boolean> publishToDefaultTopic(
            @Parameter(description = "消息内容", required = true) @RequestParam String payload) {
        
        try {
            mqttService.publishToDefaultTopic(payload);
            log.info("通过API发布消息到默认主题成功: payload={}", payload);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("通过API发布消息到默认主题失败: payload={}", payload, e);
            return CommonResult.error("发布消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe")
    @Operation(summary = "订阅MQTT主题")
    public CommonResult<Boolean> subscribeTopic(
            @Parameter(description = "主题", required = true) @RequestParam String topic,
            @Parameter(description = "QoS等级", required = false) @RequestParam(defaultValue = "1") int qos) {
        
        try {
            mqttService.subscribeTopic(topic, qos);
            
            // 添加消息处理器，将接收到的消息存储起来
            mqttService.addMessageHandler(topic, new MqttSubscriber.MqttMessageHandler() {
                @Override
                public void handleMessage(String receivedTopic, String payload, org.eclipse.paho.client.mqttv3.MqttMessage message) {
                    receivedMessages.put(receivedTopic + "_" + System.currentTimeMillis(), payload);
                    log.info("通过API订阅收到消息: topic={}, payload={}", receivedTopic, payload);
                }
            });
            
            log.info("通过API订阅MQTT主题成功: topic={}, qos={}", topic, qos);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("通过API订阅MQTT主题失败: topic={}, qos={}", topic, qos, e);
            return CommonResult.error("订阅主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/unsubscribe")
    @Operation(summary = "取消订阅MQTT主题")
    public CommonResult<Boolean> unsubscribeTopic(
            @Parameter(description = "主题", required = true) @RequestParam String topic) {
        
        try {
            mqttService.unsubscribeTopic(topic);
            log.info("通过API取消订阅MQTT主题成功: topic={}", topic);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("通过API取消订阅MQTT主题失败: topic={}", topic, e);
            return CommonResult.error("取消订阅主题失败: " + e.getMessage());
        }
    }

    @PostMapping("/subscribe/batch")
    @Operation(summary = "批量订阅MQTT主题")
    public CommonResult<Boolean> subscribeTopics(
            @Parameter(description = "主题列表", required = true) @RequestBody List<String> topics) {
        
        try {
            mqttService.subscribeTopics(topics);
            log.info("通过API批量订阅MQTT主题成功: topics={}", topics);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("通过API批量订阅MQTT主题失败: topics={}", topics, e);
            return CommonResult.error("批量订阅主题失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "获取MQTT连接状态")
    public CommonResult<Boolean> getConnectionStatus() {
        try {
            boolean connected = mqttService.isConnected();
            return CommonResult.success(connected);
        } catch (Exception e) {
            log.error("获取MQTT连接状态失败", e);
            return CommonResult.error("获取连接状态失败: " + e.getMessage());
        }
    }

    @GetMapping("/messages")
    @Operation(summary = "获取接收到的消息")
    public CommonResult<Map<String, String>> getReceivedMessages() {
        try {
            return CommonResult.success(receivedMessages);
        } catch (Exception e) {
            log.error("获取接收到的消息失败", e);
            return CommonResult.error("获取消息失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/messages")
    @Operation(summary = "清空接收到的消息")
    public CommonResult<Boolean> clearReceivedMessages() {
        try {
            receivedMessages.clear();
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("清空接收到的消息失败", e);
            return CommonResult.error("清空消息失败: " + e.getMessage());
        }
    }

    @PostMapping("/test")
    @Operation(summary = "MQTT功能测试")
    public CommonResult<String> testMqtt() {
        try {
            String testTopic = "dam/test";
            String testMessage = "Hello MQTT! Time: " + System.currentTimeMillis();
            
            // 先订阅测试主题
            mqttService.subscribeTopic(testTopic);
            
            // 等待一下确保订阅成功
            Thread.sleep(1000);
            
            // 发布测试消息
            mqttService.publishMessage(testTopic, testMessage);
            
            return CommonResult.success("MQTT测试完成，已发布消息到主题: " + testTopic + "，消息内容: " + testMessage);
        } catch (Exception e) {
            log.error("MQTT功能测试失败", e);
            return CommonResult.error("测试失败: " + e.getMessage());
        }
    }
}
