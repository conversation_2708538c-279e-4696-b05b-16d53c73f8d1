package cn.powerchina.bjy.link.dam.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * MQTT工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class MqttUtils {

    // MQTT主题名称的正则表达式
    private static final Pattern TOPIC_PATTERN = Pattern.compile("^[^#+\\s]+$");
    
    // MQTT通配符主题的正则表达式
    private static final Pattern WILDCARD_TOPIC_PATTERN = Pattern.compile("^[^\\s]*[#+]?[^\\s]*$");

    /**
     * 验证MQTT主题名称是否有效
     *
     * @param topic 主题名称
     * @return 是否有效
     */
    public static boolean isValidTopic(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            return false;
        }
        
        // 主题不能包含通配符
        return TOPIC_PATTERN.matcher(topic).matches();
    }

    /**
     * 验证MQTT订阅主题是否有效（可以包含通配符）
     *
     * @param topic 订阅主题
     * @return 是否有效
     */
    public static boolean isValidSubscribeTopic(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            return false;
        }
        
        return WILDCARD_TOPIC_PATTERN.matcher(topic).matches();
    }

    /**
     * 检查主题是否匹配订阅模式（支持MQTT通配符）
     *
     * @param actualTopic 实际主题
     * @param filterTopic 订阅模式（可包含通配符）
     * @return 是否匹配
     */
    public static boolean isTopicMatch(String actualTopic, String filterTopic) {
        if (actualTopic == null || filterTopic == null) {
            return false;
        }

        // 完全匹配
        if (actualTopic.equals(filterTopic)) {
            return true;
        }

        // 多级通配符 #
        if (filterTopic.equals("#")) {
            return true;
        }

        // 处理包含通配符的情况
        if (filterTopic.contains("#") || filterTopic.contains("+")) {
            return matchWithWildcards(actualTopic, filterTopic);
        }

        return false;
    }

    /**
     * 使用通配符匹配主题
     */
    private static boolean matchWithWildcards(String actualTopic, String filterTopic) {
        String[] actualParts = actualTopic.split("/");
        String[] filterParts = filterTopic.split("/");

        int actualIndex = 0;
        int filterIndex = 0;

        while (actualIndex < actualParts.length && filterIndex < filterParts.length) {
            String filterPart = filterParts[filterIndex];

            if (filterPart.equals("#")) {
                // 多级通配符匹配剩余所有级别
                return true;
            } else if (filterPart.equals("+")) {
                // 单级通配符匹配一个级别
                actualIndex++;
                filterIndex++;
            } else if (filterPart.equals(actualParts[actualIndex])) {
                // 精确匹配
                actualIndex++;
                filterIndex++;
            } else {
                // 不匹配
                return false;
            }
        }

        // 检查是否都匹配完了
        return actualIndex == actualParts.length && filterIndex == filterParts.length;
    }

    /**
     * 验证QoS等级是否有效
     *
     * @param qos QoS等级
     * @return 是否有效
     */
    public static boolean isValidQos(int qos) {
        return qos >= 0 && qos <= 2;
    }

    /**
     * 构建设备数据主题
     *
     * @param deviceId 设备ID
     * @return 主题名称
     */
    public static String buildDeviceDataTopic(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        return "dam/data/device/" + deviceId.trim();
    }

    /**
     * 构建设备告警主题
     *
     * @param deviceId 设备ID
     * @return 主题名称
     */
    public static String buildDeviceAlarmTopic(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        return "dam/alarm/device/" + deviceId.trim();
    }

    /**
     * 构建设备控制主题
     *
     * @param deviceId 设备ID
     * @return 主题名称
     */
    public static String buildDeviceControlTopic(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        return "dam/control/device/" + deviceId.trim();
    }

    /**
     * 构建设备状态主题
     *
     * @param deviceId 设备ID
     * @return 主题名称
     */
    public static String buildDeviceStatusTopic(String deviceId) {
        if (deviceId == null || deviceId.trim().isEmpty()) {
            throw new IllegalArgumentException("设备ID不能为空");
        }
        return "dam/status/device/" + deviceId.trim();
    }

    /**
     * 构建项目数据主题
     *
     * @param projectId 项目ID
     * @return 主题名称
     */
    public static String buildProjectDataTopic(String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        return "dam/data/project/" + projectId.trim();
    }

    /**
     * 从主题中提取设备ID
     *
     * @param topic 主题名称
     * @return 设备ID，如果无法提取则返回null
     */
    public static String extractDeviceIdFromTopic(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            return null;
        }

        // 匹配 dam/data/device/{deviceId} 格式
        if (topic.startsWith("dam/data/device/")) {
            return topic.substring("dam/data/device/".length());
        }
        
        // 匹配 dam/alarm/device/{deviceId} 格式
        if (topic.startsWith("dam/alarm/device/")) {
            return topic.substring("dam/alarm/device/".length());
        }
        
        // 匹配 dam/control/device/{deviceId} 格式
        if (topic.startsWith("dam/control/device/")) {
            return topic.substring("dam/control/device/".length());
        }
        
        // 匹配 dam/status/device/{deviceId} 格式
        if (topic.startsWith("dam/status/device/")) {
            return topic.substring("dam/status/device/".length());
        }

        return null;
    }

    /**
     * 从主题中提取项目ID
     *
     * @param topic 主题名称
     * @return 项目ID，如果无法提取则返回null
     */
    public static String extractProjectIdFromTopic(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            return null;
        }

        // 匹配 dam/data/project/{projectId} 格式
        if (topic.startsWith("dam/data/project/")) {
            return topic.substring("dam/data/project/".length());
        }

        return null;
    }

    /**
     * 清理主题名称（移除前后空格，替换非法字符）
     *
     * @param topic 原始主题名称
     * @return 清理后的主题名称
     */
    public static String cleanTopic(String topic) {
        if (topic == null) {
            return null;
        }
        
        return topic.trim().replaceAll("\\s+", "_");
    }

    /**
     * 生成唯一的客户端ID
     *
     * @param prefix 前缀
     * @return 唯一的客户端ID
     */
    public static String generateClientId(String prefix) {
        if (prefix == null || prefix.trim().isEmpty()) {
            prefix = "mqtt-client";
        }
        return prefix + "-" + System.currentTimeMillis() + "-" + 
               (int)(Math.random() * 10000);
    }
}
