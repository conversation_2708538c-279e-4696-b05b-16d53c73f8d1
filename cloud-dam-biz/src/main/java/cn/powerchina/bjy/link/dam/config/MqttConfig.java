package cn.powerchina.bjy.link.dam.config;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.UUID;

/**
 * MQTT配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MqttConfig {

    @Value("${mqtt.broker.url:tcp://localhost:1883}")
    private String brokerUrl;

    @Value("${mqtt.client.id:dam-client}")
    private String clientId;

    @Value("${mqtt.username:}")
    private String username;

    @Value("${mqtt.password:}")
    private String password;

    @Value("${mqtt.connection.timeout:10}")
    private int connectionTimeout;

    @Value("${mqtt.keep.alive.interval:60}")
    private int keepAliveInterval;

    @Value("${mqtt.clean.session:true}")
    private boolean cleanSession;

    @Value("${mqtt.automatic.reconnect:true}")
    private boolean automaticReconnect;

    /**
     * MQTT连接选项配置
     */
    @Bean
    public MqttConnectOptions mqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        
        // 设置服务器地址，支持多个地址
        if (brokerUrl.contains(",")) {
            options.setServerURIs(brokerUrl.split(","));
        } else {
            options.setServerURIs(new String[]{brokerUrl});
        }
        
        // 设置用户名和密码
        if (username != null && !username.trim().isEmpty()) {
            options.setUserName(username);
        }
        if (password != null && !password.trim().isEmpty()) {
            options.setPassword(password.toCharArray());
        }
        
        // 设置连接选项
        options.setCleanSession(cleanSession);
        options.setAutomaticReconnect(automaticReconnect);
        options.setConnectionTimeout(connectionTimeout);
        options.setKeepAliveInterval(keepAliveInterval);
        
        log.info("MQTT连接选项配置完成: brokerUrl={}, clientId={}, username={}", 
                brokerUrl, clientId, username);
        
        return options;
    }

    /**
     * MQTT客户端配置
     */
    @Bean
    public MqttClient mqttClient(MqttConnectOptions options) throws MqttException {
        // 获取第一个broker地址
        String firstBrokerUrl = brokerUrl.contains(",") ? 
                brokerUrl.split(",")[0] : brokerUrl;
        
        // 生成唯一的客户端ID
        String uniqueClientId = clientId + "-" + UUID.randomUUID().toString().replace("-", "");
        
        try {
            MqttClient client = new MqttClient(firstBrokerUrl, uniqueClientId);
            client.connect(options);
            
            log.info("MQTT客户端连接成功: brokerUrl={}, clientId={}", firstBrokerUrl, uniqueClientId);
            return client;
        } catch (MqttException e) {
            log.error("MQTT客户端连接失败: brokerUrl={}, clientId={}, error={}", 
                    firstBrokerUrl, uniqueClientId, e.getMessage());
            throw e;
        }
    }
}
