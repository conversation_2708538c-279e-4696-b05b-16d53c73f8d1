package cn.powerchina.bjy.link.dam.service.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * MQTT消息发布服务
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MqttPublisher {

    @Resource
    private MqttClient mqttClient;

    @Value("${mqtt.default.topic:dam/default}")
    private String defaultTopic;

    @Value("${mqtt.default.qos:1}")
    private int defaultQos;

    @Value("${mqtt.default.retained:false}")
    private boolean defaultRetained;

    /**
     * 发布消息到指定主题（使用默认QoS和retained设置）
     *
     * @param topic   主题
     * @param payload 消息内容
     */
    public void publish(String topic, String payload) {
        publish(topic, payload, defaultQos, defaultRetained);
    }

    /**
     * 发布消息到指定主题
     *
     * @param topic    主题
     * @param payload  消息内容
     * @param qos      服务质量等级 (0, 1, 2)
     * @param retained 是否保留消息
     */
    public void publish(String topic, String payload, int qos, boolean retained) {
        if (topic == null || topic.trim().isEmpty()) {
            log.error("发布消息失败: topic不能为空");
            return;
        }

        if (payload == null) {
            payload = "";
        }

        try {
            // 检查客户端连接状态
            if (!mqttClient.isConnected()) {
                log.warn("MQTT客户端未连接，尝试重新连接...");
                mqttClient.reconnect();
            }

            MqttMessage message = new MqttMessage(payload.getBytes(StandardCharsets.UTF_8));
            message.setQos(qos);
            message.setRetained(retained);

            mqttClient.publish(topic, message);
            
            log.info("MQTT消息发布成功: topic={}, payload={}, qos={}, retained={}", 
                    topic, payload, qos, retained);
                    
        } catch (MqttException e) {
            log.error("MQTT消息发布失败: topic={}, payload={}, error={}", 
                    topic, payload, e.getMessage(), e);
            throw new RuntimeException("MQTT消息发布失败", e);
        }
    }

    /**
     * 发布消息到默认主题
     *
     * @param payload 消息内容
     */
    public void publishToDefaultTopic(String payload) {
        publish(defaultTopic, payload);
    }

    /**
     * 发布JSON消息到指定主题
     *
     * @param topic  主题
     * @param object 要序列化为JSON的对象
     */
    public void publishJson(String topic, Object object) {
        if (object == null) {
            publish(topic, "");
            return;
        }

        try {
            // 这里可以使用项目中的JSON工具类
            String jsonPayload = object.toString(); // 简单实现，实际项目中应使用JSON序列化
            publish(topic, jsonPayload);
        } catch (Exception e) {
            log.error("JSON消息发布失败: topic={}, object={}, error={}", 
                    topic, object, e.getMessage(), e);
            throw new RuntimeException("JSON消息发布失败", e);
        }
    }

    /**
     * 批量发布消息
     *
     * @param messages 消息列表，每个元素包含topic和payload
     */
    public void publishBatch(java.util.List<MqttMessageInfo> messages) {
        if (messages == null || messages.isEmpty()) {
            log.warn("批量发布消息失败: 消息列表为空");
            return;
        }

        int successCount = 0;
        int failCount = 0;

        for (MqttMessageInfo messageInfo : messages) {
            try {
                publish(messageInfo.getTopic(), messageInfo.getPayload(), 
                       messageInfo.getQos(), messageInfo.isRetained());
                successCount++;
            } catch (Exception e) {
                failCount++;
                log.error("批量发布消息中的单条消息失败: topic={}, payload={}", 
                        messageInfo.getTopic(), messageInfo.getPayload(), e);
            }
        }

        log.info("批量发布消息完成: 总数={}, 成功={}, 失败={}", 
                messages.size(), successCount, failCount);
    }

    /**
     * 检查MQTT客户端连接状态
     *
     * @return 是否已连接
     */
    public boolean isConnected() {
        return mqttClient != null && mqttClient.isConnected();
    }

    /**
     * MQTT消息信息类
     */
    public static class MqttMessageInfo {
        private String topic;
        private String payload;
        private int qos = 1;
        private boolean retained = false;

        public MqttMessageInfo() {}

        public MqttMessageInfo(String topic, String payload) {
            this.topic = topic;
            this.payload = payload;
        }

        public MqttMessageInfo(String topic, String payload, int qos, boolean retained) {
            this.topic = topic;
            this.payload = payload;
            this.qos = qos;
            this.retained = retained;
        }

        // Getters and Setters
        public String getTopic() { return topic; }
        public void setTopic(String topic) { this.topic = topic; }
        public String getPayload() { return payload; }
        public void setPayload(String payload) { this.payload = payload; }
        public int getQos() { return qos; }
        public void setQos(int qos) { this.qos = qos; }
        public boolean isRetained() { return retained; }
        public void setRetained(boolean retained) { this.retained = retained; }
    }
}
