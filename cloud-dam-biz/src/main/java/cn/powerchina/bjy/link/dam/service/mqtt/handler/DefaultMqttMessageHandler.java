package cn.powerchina.bjy.link.dam.service.mqtt.handler;

import cn.powerchina.bjy.link.dam.service.mqtt.MqttSubscriber;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * 默认MQTT消息处理器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefaultMqttMessageHandler implements MqttSubscriber.MqttMessageHandler {

    @Resource
    private MqttSubscriber mqttSubscriber;

    @PostConstruct
    public void init() {
        // 注册处理器到订阅服务
        mqttSubscriber.addMessageHandler("dam/#", this);
        log.info("默认MQTT消息处理器已注册，监听主题: dam/#");
    }

    @Override
    public void handleMessage(String topic, String payload, MqttMessage message) {
        try {
            log.info("默认处理器收到MQTT消息: topic={}, payload={}, qos={}, retained={}", 
                    topic, payload, message.getQos(), message.isRetained());

            // 根据不同的主题进行不同的处理
            if (topic.startsWith("dam/data/")) {
                handleDataMessage(topic, payload, message);
            } else if (topic.startsWith("dam/alarm/")) {
                handleAlarmMessage(topic, payload, message);
            } else if (topic.startsWith("dam/control/")) {
                handleControlMessage(topic, payload, message);
            } else if (topic.startsWith("dam/status/")) {
                handleStatusMessage(topic, payload, message);
            } else {
                handleGenericMessage(topic, payload, message);
            }

        } catch (Exception e) {
            log.error("处理MQTT消息失败: topic={}, payload={}", topic, payload, e);
        }
    }

    /**
     * 处理数据消息
     */
    private void handleDataMessage(String topic, String payload, MqttMessage message) {
        log.info("处理数据消息: topic={}, payload={}", topic, payload);
        
        // 这里可以添加具体的数据处理逻辑
        // 例如：解析JSON数据，存储到数据库，触发业务逻辑等
        
        try {
            // 示例：解析设备数据
            // DeviceData deviceData = JsonUtils.parseObject(payload, DeviceData.class);
            // deviceDataService.saveDeviceData(deviceData);
            
            log.debug("数据消息处理完成: topic={}", topic);
        } catch (Exception e) {
            log.error("数据消息处理失败: topic={}, payload={}", topic, payload, e);
        }
    }

    /**
     * 处理告警消息
     */
    private void handleAlarmMessage(String topic, String payload, MqttMessage message) {
        log.warn("收到告警消息: topic={}, payload={}", topic, payload);
        
        // 这里可以添加告警处理逻辑
        // 例如：发送通知，记录告警日志，触发告警流程等
        
        try {
            // 示例：处理告警
            // AlarmInfo alarmInfo = JsonUtils.parseObject(payload, AlarmInfo.class);
            // alarmService.processAlarm(alarmInfo);
            
            log.debug("告警消息处理完成: topic={}", topic);
        } catch (Exception e) {
            log.error("告警消息处理失败: topic={}, payload={}", topic, payload, e);
        }
    }

    /**
     * 处理控制消息
     */
    private void handleControlMessage(String topic, String payload, MqttMessage message) {
        log.info("收到控制消息: topic={}, payload={}", topic, payload);
        
        // 这里可以添加设备控制逻辑
        // 例如：设备指令下发，参数配置等
        
        try {
            // 示例：处理控制指令
            // ControlCommand command = JsonUtils.parseObject(payload, ControlCommand.class);
            // deviceControlService.executeCommand(command);
            
            log.debug("控制消息处理完成: topic={}", topic);
        } catch (Exception e) {
            log.error("控制消息处理失败: topic={}, payload={}", topic, payload, e);
        }
    }

    /**
     * 处理状态消息
     */
    private void handleStatusMessage(String topic, String payload, MqttMessage message) {
        log.info("收到状态消息: topic={}, payload={}", topic, payload);
        
        // 这里可以添加状态更新逻辑
        // 例如：设备在线状态，运行状态等
        
        try {
            // 示例：更新设备状态
            // DeviceStatus status = JsonUtils.parseObject(payload, DeviceStatus.class);
            // deviceService.updateDeviceStatus(status);
            
            log.debug("状态消息处理完成: topic={}", topic);
        } catch (Exception e) {
            log.error("状态消息处理失败: topic={}, payload={}", topic, payload, e);
        }
    }

    /**
     * 处理通用消息
     */
    private void handleGenericMessage(String topic, String payload, MqttMessage message) {
        log.info("收到通用消息: topic={}, payload={}", topic, payload);
        
        // 这里可以添加通用消息处理逻辑
        
        try {
            // 示例：记录消息日志
            // messageLogService.saveMessage(topic, payload, message.getQos(), message.isRetained());
            
            log.debug("通用消息处理完成: topic={}", topic);
        } catch (Exception e) {
            log.error("通用消息处理失败: topic={}, payload={}", topic, payload, e);
        }
    }
}
